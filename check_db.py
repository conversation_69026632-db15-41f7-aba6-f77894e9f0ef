import sqlite3

# Connect to database
db = sqlite3.connect('vishnorex.db')
db.row_factory = sqlite3.Row
cursor = db.cursor()

# Check staff records
print("=== STAFF RECORDS ===")
staff = cursor.execute('SELECT id, staff_id, full_name, school_id FROM staff ORDER BY id').fetchall()
for s in staff:
    print(f"ID: {s['id']}, Staff ID: {s['staff_id']}, Name: {s['full_name']}, School: {s['school_id']}")

print(f"\nTotal staff: {len(staff)}")

# Check schools
print("\n=== SCHOOLS ===")
schools = cursor.execute('SELECT id, name FROM schools ORDER BY id').fetchall()
for school in schools:
    print(f"ID: {school['id']}, Name: {school['name']}")

# Check admins
print("\n=== ADMINS ===")
admins = cursor.execute('SELECT id, username, full_name, school_id FROM admins ORDER BY id').fetchall()
for admin in admins:
    print(f"ID: {admin['id']}, Username: {admin['username']}, Name: {admin['full_name']}, School: {admin['school_id']}")

db.close()
