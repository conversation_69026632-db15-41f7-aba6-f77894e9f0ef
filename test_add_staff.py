import sqlite3
import datetime
from werkzeug.security import generate_password_hash

# Connect to database
db = sqlite3.connect('vishnorex.db')
cursor = db.cursor()

# Add a new test staff member to school 4 (<PERSON><PERSON><PERSON><PERSON>)
staff_id = "TEST123"
full_name = "Test Staff Member"
password_hash = generate_password_hash("password123")
email = "<EMAIL>"
phone = "1234567890"
department = "Testing"
position = "Test Position"
school_id = 4

try:
    cursor.execute('''
        INSERT INTO staff
        (school_id, staff_id, password, password_hash, full_name, email, phone, department, position)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', (school_id, staff_id, password_hash, password_hash, full_name, email, phone, department, position))
    
    db.commit()
    print(f"Successfully added test staff member: {full_name} (ID: {staff_id})")
    
    # Verify the addition
    staff = cursor.execute('SELECT * FROM staff WHERE staff_id = ?', (staff_id,)).fetchone()
    if staff:
        print(f"Verification: Staff member found in database with ID {staff[0]}")
    else:
        print("Error: Staff member not found after insertion")
        
except sqlite3.IntegrityError as e:
    print(f"Staff member already exists or integrity error: {e}")
except Exception as e:
    print(f"Error adding staff member: {e}")
finally:
    db.close()
