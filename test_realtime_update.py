import requests
import json

# Test the real-time attendance endpoint
url = "http://127.0.0.1:5000/get_realtime_attendance"

try:
    # This will fail without proper session, but we can see if the endpoint exists
    response = requests.get(url)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.text}")
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            print(f"Found {len(data.get('attendance_data', []))} staff members")
            for staff in data.get('attendance_data', []):
                print(f"- {staff.get('full_name')} (ID: {staff.get('staff_id')})")
        else:
            print(f"API Error: {data.get('error')}")
    else:
        print("Endpoint requires authentication")
        
except requests.exceptions.ConnectionError:
    print("Could not connect to Flask app. Make sure it's running on http://127.0.0.1:5000")
except Exception as e:
    print(f"Error: {e}")
